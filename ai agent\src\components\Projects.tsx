
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ExternalLink, Github, Play } from "lucide-react";

export const Projects = () => {
  const [filter, setFilter] = useState("all");
  
  const projects = [
    {
      title: "E-commerce Platform",
      description: "Full-stack e-commerce solution with AI-powered recommendations and real-time analytics",
      category: "web",
      image: "🛒",
      tech: ["React", "Node.js", "AI/ML", "PostgreSQL"],
      link: "#",
      github: "#",
      gradient: "from-blue-600 to-cyan-600"
    },
    {
      title: "Mobile Banking App",
      description: "Secure mobile banking application with biometric authentication and blockchain integration",
      category: "mobile",
      image: "🏦",
      tech: ["React Native", "Blockchain", "Security", "MongoDB"],
      link: "#",
      github: "#",
      gradient: "from-purple-600 to-pink-600"
    },
    {
      title: "AI Code Assistant",
      description: "Intelligent code completion and generation tool with multi-language support",
      category: "ai",
      image: "🤖",
      tech: ["Python", "TensorFlow", "OpenAI", "FastAPI"],
      link: "#",
      github: "#",
      gradient: "from-emerald-600 to-teal-600"
    },
    {
      title: "Corporate Dashboard",
      description: "Real-time analytics dashboard for enterprise clients with advanced data visualization",
      category: "web",
      image: "📊",
      tech: ["Vue.js", "D3.js", "MongoDB", "WebSocket"],
      link: "#",
      github: "#",
      gradient: "from-orange-600 to-red-600"
    },
    {
      title: "Smart IoT Platform",
      description: "IoT device management and monitoring system with predictive analytics",
      category: "iot",
      image: "🌐",
      tech: ["Python", "AWS IoT", "Machine Learning", "Redis"],
      link: "#",
      github: "#",
      gradient: "from-indigo-600 to-purple-600"
    },
    {
      title: "Healthcare AI Assistant",
      description: "AI-powered diagnostic assistance for healthcare professionals with HIPAA compliance",
      category: "ai",
      image: "🏥",
      tech: ["Python", "TensorFlow", "Healthcare APIs", "Docker"],
      link: "#",
      github: "#",
      gradient: "from-pink-600 to-rose-600"
    }
  ];

  const categories = [
    { id: "all", label: "All Projects", gradient: "from-slate-600 to-slate-700" },
    { id: "web", label: "Web Development", gradient: "from-blue-600 to-cyan-600" },
    { id: "mobile", label: "Mobile Apps", gradient: "from-purple-600 to-pink-600" },
    { id: "ai", label: "AI/ML", gradient: "from-emerald-600 to-teal-600" },
    { id: "iot", label: "IoT", gradient: "from-orange-600 to-red-600" }
  ];

  const filteredProjects = filter === "all" 
    ? projects 
    : projects.filter(project => project.category === filter);

  return (
    <section id="projects" className="py-32 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/80 to-slate-950"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(59,130,246,0.1),transparent_50%)]"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="inline-flex items-center justify-center p-2 bg-gradient-to-r from-blue-600/20 to-cyan-600/20 rounded-full mb-8 backdrop-blur-md border border-blue-500/20">
            <div className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white px-6 py-2 rounded-full text-sm font-medium">
              Our Portfolio
            </div>
          </div>
          
          <h2 className="text-5xl md:text-7xl font-bold mb-8">
            <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-teal-400 bg-clip-text text-transparent">
              Featured Projects
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12 leading-relaxed">
            Explore our portfolio of cutting-edge projects that showcase the power of AI-driven development 
            and innovative solutions.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={filter === category.id ? "default" : "outline"}
                onClick={() => setFilter(category.id)}
                className={filter === category.id 
                  ? `bg-gradient-to-r ${category.gradient} text-white shadow-lg hover:shadow-xl transition-all duration-300` 
                  : "border-slate-600 text-slate-300 hover:text-white hover:bg-slate-800/50 backdrop-blur-md transition-all duration-300"
                }
              >
                {category.label}
              </Button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <div 
              key={index}
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-xl border border-slate-700/50 hover:border-blue-500/50 transition-all duration-500 hover:scale-105"
            >
              {/* Project image/icon area */}
              <div className={`relative aspect-video bg-gradient-to-br ${project.gradient} p-8 flex items-center justify-center overflow-hidden`}>
                <div className="text-8xl group-hover:scale-110 transition-transform duration-300">
                  {project.image}
                </div>
                
                {/* Overlay with action buttons */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                  <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10 backdrop-blur-md">
                    <Play className="w-4 h-4 mr-1" />
                    Demo
                  </Button>
                  <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10 backdrop-blur-md">
                    <Github className="w-4 h-4 mr-1" />
                    Code
                  </Button>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors">
                  {project.title}
                </h3>
                <p className="text-slate-300 mb-4 text-sm leading-relaxed">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.tech.map((tech, techIndex) => (
                    <span 
                      key={techIndex}
                      className="px-3 py-1 bg-slate-700/50 text-slate-300 rounded-full text-xs backdrop-blur-md border border-slate-600/50 hover:border-blue-500/50 transition-colors"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="flex space-x-3">
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="flex-1 border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50 backdrop-blur-md group/btn"
                  >
                    View Project
                    <ExternalLink className="ml-2 w-3 h-3 group-hover/btn:translate-x-0.5 group-hover/btn:-translate-y-0.5 transition-transform" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
