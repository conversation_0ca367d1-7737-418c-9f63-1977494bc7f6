
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, TestTube, Copy, Eye, EyeOff, Sparkles, Zap, Bot, Code2, Cpu } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AIProvider {
  id: string;
  name: string;
  apiKey: string;
  model: string;
  endpoint?: string;
  provider: string;
  status?: 'active' | 'testing' | 'error';
}

export const Trail = () => {
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [newProvider, setNewProvider] = useState({
    name: "",
    apiKey: "",
    model: "",
    endpoint: "",
    provider: "openai"
  });
  const [isAddingProvider, setIsAddingProvider] = useState(false);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [generatedCode, setGeneratedCode] = useState("");
  const [codePrompt, setCodePrompt] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [showApiKey, setShowApiKey] = useState<{ [key: string]: boolean }>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const providerOptions = [
    { value: "openai", label: "OpenAI", icon: "🤖", color: "from-emerald-400 to-teal-500" },
    { value: "anthropic", label: "Anthropic", icon: "🧠", color: "from-orange-400 to-red-500" },
    { value: "google", label: "Google AI", icon: "🌟", color: "from-blue-400 to-indigo-500" },
    { value: "cohere", label: "Cohere", icon: "⚡", color: "from-purple-400 to-pink-500" },
    { value: "custom", label: "Custom", icon: "🔧", color: "from-gray-400 to-slate-500" }
  ];

  const modelOptions = {
    openai: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o"],
    anthropic: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
    google: ["gemini-pro", "gemini-pro-vision"],
    cohere: ["command", "command-light"],
    custom: ["custom-model"]
  };

  const addProvider = () => {
    if (!newProvider.name || !newProvider.apiKey || !newProvider.model) {
      toast({
        title: "⚠️ Missing Information",
        description: "Please fill in all required fields to continue",
        variant: "destructive"
      });
      return;
    }

    const provider: AIProvider = {
      id: Date.now().toString(),
      ...newProvider,
      status: 'active'
    };

    setProviders(prev => [...prev, provider]);
    setNewProvider({ name: "", apiKey: "", model: "", endpoint: "", provider: "openai" });
    setIsAddingProvider(false);
    
    toast({
      title: "✨ Provider Added",
      description: `${newProvider.name} is ready for code generation`
    });
  };

  const removeProvider = (id: string) => {
    setProviders(prev => prev.filter(p => p.id !== id));
    toast({
      title: "🗑️ Provider Removed",
      description: "AI provider has been successfully removed"
    });
  };

  const testProvider = async (provider: AIProvider) => {
    setTestingProvider(provider.id);
    setProviders(prev => prev.map(p => 
      p.id === provider.id ? { ...p, status: 'testing' } : p
    ));
    
    // Simulate API test with more realistic timing
    setTimeout(() => {
      setTestingProvider(null);
      const success = Math.random() > 0.1; // 90% success rate
      setProviders(prev => prev.map(p => 
        p.id === provider.id ? { ...p, status: success ? 'active' : 'error' } : p
      ));
      
      toast({
        title: success ? "✅ Connection Successful" : "❌ Connection Failed",
        description: success 
          ? `${provider.name} is responding correctly` 
          : "Please check your API key and endpoint",
        variant: success ? "default" : "destructive"
      });
    }, 2500);
  };

  const generateCode = async () => {
    if (!selectedProvider || !codePrompt) {
      toast({
        title: "⚠️ Missing Requirements",
        description: "Please select a provider and enter a prompt",
        variant: "destructive"
      });
      return;
    }

    const provider = providers.find(p => p.id === selectedProvider);
    if (!provider) return;

    setIsGenerating(true);

    // Simulate realistic code generation timing
    setTimeout(() => {
      const mockCode = `// ✨ Generated with ${provider.name} (${provider.model})
// Prompt: "${codePrompt}"

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const GeneratedComponent = () => {
  const [isActive, setIsActive] = useState(false);
  
  useEffect(() => {
    // Initialization logic
    console.log('Component mounted with futuristic design');
  }, []);

  return (
    <motion.div 
      className="relative p-8 bg-gradient-to-br from-slate-900/80 to-blue-900/40 
                 backdrop-blur-lg border border-white/10 rounded-2xl shadow-2xl"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse" />
        <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-blue-200 
                       bg-clip-text text-transparent">
          AI Generated Component
        </h2>
      </div>
      
      <p className="text-slate-300 mb-4 leading-relaxed">
        This component was generated based on your prompt: "{codePrompt}"
      </p>
      
      <motion.button
        className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 
                   text-white rounded-lg font-medium hover:shadow-lg 
                   hover:shadow-blue-500/25 transition-all duration-300"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsActive(!isActive)}
      >
        {isActive ? 'Deactivate' : 'Activate'} ⚡
      </motion.button>
      
      {isActive && (
        <motion.div 
          className="mt-4 p-4 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 
                     border border-emerald-400/20 rounded-lg"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.4 }}
        >
          <span className="text-emerald-400">✨ Component is now active!</span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default GeneratedComponent;`;

      setGeneratedCode(mockCode);
      setIsGenerating(false);
      toast({
        title: "🚀 Code Generated Successfully",
        description: `Generated with ${provider.name} in ${(Math.random() * 3 + 1).toFixed(1)}s`
      });
    }, 3000);
  };

  const copyCode = () => {
    navigator.clipboard.writeText(generatedCode);
    toast({
      title: "📋 Code Copied",
      description: "Code has been copied to your clipboard"
    });
  };

  const toggleApiKeyVisibility = (providerId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [providerId]: !prev[providerId]
    }));
  };

  const getProviderConfig = (providerType: string) => {
    return providerOptions.find(p => p.value === providerType) || providerOptions[0];
  };

  return (
    <section id="trail" className="py-24 bg-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/10 to-slate-900"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(139,92,246,0.1),transparent_50%)]"></div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 mb-4">
            <Sparkles className="w-8 h-8 text-blue-400" />
            <Bot className="w-8 h-8 text-purple-400" />
            <Cpu className="w-8 h-8 text-emerald-400" />
          </div>
          <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400 bg-clip-text text-transparent mb-6">
            AI Trail Lab
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Configure your custom AI providers and unleash the power of intelligent code generation
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Providers Management */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Zap className="w-6 h-6 text-blue-400" />
                <h3 className="text-2xl font-semibold text-white">AI Providers</h3>
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-400/30">
                  {providers.length}
                </Badge>
              </div>
              
              <Dialog open={isAddingProvider} onOpenChange={setIsAddingProvider}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-blue-500/25 transition-all duration-300">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Provider
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 shadow-2xl">
                  <DialogHeader>
                    <DialogTitle className="text-white text-xl flex items-center space-x-2">
                      <Bot className="w-5 h-5 text-blue-400" />
                      <span>Add AI Provider</span>
                    </DialogTitle>
                    <DialogDescription className="text-slate-300">
                      Configure your custom AI provider for intelligent code generation
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="provider-type" className="text-white mb-2 block">Provider Type</Label>
                      <Select 
                        value={newProvider.provider} 
                        onValueChange={(value) => setNewProvider(prev => ({ ...prev, provider: value, model: "" }))}
                      >
                        <SelectTrigger className="bg-slate-800/50 border-slate-600/50 text-white backdrop-blur">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800/95 backdrop-blur-xl border-slate-600/50">
                          {providerOptions.map(option => (
                            <SelectItem key={option.value} value={option.value} className="text-white hover:bg-slate-700/50">
                              <div className="flex items-center space-x-2">
                                <span>{option.icon}</span>
                                <span>{option.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="provider-name" className="text-white mb-2 block">Provider Name</Label>
                      <Input
                        id="provider-name"
                        value={newProvider.name}
                        onChange={(e) => setNewProvider(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="My AI Instance"
                        className="bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400 backdrop-blur"
                      />
                    </div>

                    <div>
                      <Label htmlFor="api-key" className="text-white mb-2 block">API Key</Label>
                      <Input
                        id="api-key"
                        type="password"
                        value={newProvider.apiKey}
                        onChange={(e) => setNewProvider(prev => ({ ...prev, apiKey: e.target.value }))}
                        placeholder="sk-..."
                        className="bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400 backdrop-blur"
                      />
                    </div>

                    <div>
                      <Label htmlFor="model" className="text-white mb-2 block">Model</Label>
                      <Select 
                        value={newProvider.model} 
                        onValueChange={(value) => setNewProvider(prev => ({ ...prev, model: value }))}
                      >
                        <SelectTrigger className="bg-slate-800/50 border-slate-600/50 text-white backdrop-blur">
                          <SelectValue placeholder="Select a model" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800/95 backdrop-blur-xl border-slate-600/50">
                          {modelOptions[newProvider.provider as keyof typeof modelOptions]?.map(model => (
                            <SelectItem key={model} value={model} className="text-white hover:bg-slate-700/50">
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {newProvider.provider === "custom" && (
                      <div>
                        <Label htmlFor="endpoint" className="text-white mb-2 block">Custom Endpoint</Label>
                        <Input
                          id="endpoint"
                          value={newProvider.endpoint}
                          onChange={(e) => setNewProvider(prev => ({ ...prev, endpoint: e.target.value }))}
                          placeholder="https://api.example.com/v1"
                          className="bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400 backdrop-blur"
                        />
                      </div>
                    )}

                    <Button onClick={addProvider} className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Provider
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            <div className="space-y-4">
              {providers.map(provider => {
                const config = getProviderConfig(provider.provider);
                return (
                  <Card key={provider.id} className="bg-slate-800/30 backdrop-blur-lg border border-slate-700/50 hover:border-blue-500/30 transition-all duration-300 shadow-xl">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${config.color} flex items-center justify-center text-lg`}>
                            {config.icon}
                          </div>
                          <div>
                            <CardTitle className="text-white text-lg">{provider.name}</CardTitle>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge 
                                variant={provider.status === 'active' ? 'default' : provider.status === 'testing' ? 'secondary' : 'destructive'}
                                className={`text-xs ${
                                  provider.status === 'active' ? 'bg-emerald-500/20 text-emerald-300 border-emerald-400/30' :
                                  provider.status === 'testing' ? 'bg-yellow-500/20 text-yellow-300 border-yellow-400/30' :
                                  'bg-red-500/20 text-red-300 border-red-400/30'
                                }`}
                              >
                                {provider.status === 'active' ? '✅ Active' : 
                                 provider.status === 'testing' ? '🔄 Testing' : 
                                 '❌ Error'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => testProvider(provider)}
                            disabled={testingProvider === provider.id}
                            className="border-slate-600/50 text-slate-300 hover:bg-slate-700/50 backdrop-blur"
                          >
                            <TestTube className="w-4 h-4 mr-1" />
                            {testingProvider === provider.id ? "Testing..." : "Test"}
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => removeProvider(provider.id)}
                            className="bg-red-500/20 hover:bg-red-500/30 border-red-400/30"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2 text-sm text-slate-300">
                        <div className="flex items-center space-x-2">
                          <span className="text-slate-400">Provider:</span>
                          <Badge variant="outline" className="border-slate-600/50 text-slate-300">
                            {provider.provider}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-slate-400">Model:</span>
                          <Badge variant="outline" className="border-slate-600/50 text-slate-300">
                            {provider.model}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-slate-400">API Key:</span>
                          <span className="font-mono text-xs bg-slate-700/50 px-2 py-1 rounded">
                            {showApiKey[provider.id] ? provider.apiKey : "••••••••••••••••"}
                          </span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleApiKeyVisibility(provider.id)}
                            className="h-6 w-6 p-0 hover:bg-slate-700/50"
                          >
                            {showApiKey[provider.id] ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}

              {providers.length === 0 && (
                <Card className="bg-slate-800/20 backdrop-blur-lg border border-dashed border-slate-600/50 hover:border-blue-500/30 transition-all duration-300">
                  <CardContent className="py-12 text-center">
                    <Bot className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                    <p className="text-slate-400 text-lg mb-2">No AI providers configured</p>
                    <p className="text-slate-500 text-sm">Add your first AI provider to start generating intelligent code</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Code Generation */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <Code2 className="w-6 h-6 text-purple-400" />
              <h3 className="text-2xl font-semibold text-white">Code Generation</h3>
            </div>
            
            <Card className="bg-slate-800/30 backdrop-blur-lg border border-slate-700/50 shadow-xl">
              <CardHeader>
                <CardTitle className="text-white flex items-center space-x-2">
                  <Sparkles className="w-5 h-5 text-purple-400" />
                  <span>Generate Code</span>
                </CardTitle>
                <CardDescription className="text-slate-300">
                  Transform your ideas into code with AI-powered generation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="select-provider" className="text-white mb-2 block">Select AI Provider</Label>
                  <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                    <SelectTrigger className="bg-slate-700/50 border-slate-600/50 text-white backdrop-blur">
                      <SelectValue placeholder="Choose an AI provider" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800/95 backdrop-blur-xl border-slate-600/50">
                      {providers.map(provider => {
                        const config = getProviderConfig(provider.provider);
                        return (
                          <SelectItem key={provider.id} value={provider.id} className="text-white hover:bg-slate-700/50">
                            <div className="flex items-center space-x-2">
                              <span>{config.icon}</span>
                              <span>{provider.name}</span>
                              <Badge variant="outline" className="border-slate-600/50 text-slate-400 text-xs">
                                {provider.model}
                              </Badge>
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="code-prompt" className="text-white mb-2 block">Code Prompt</Label>
                  <Textarea
                    id="code-prompt"
                    value={codePrompt}
                    onChange={(e) => setCodePrompt(e.target.value)}
                    placeholder="Describe what you want to create... (e.g., 'Create a modern React component with a dark theme and animations')"
                    className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 min-h-[120px] backdrop-blur resize-none"
                  />
                </div>

                <Button 
                  onClick={generateCode} 
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-purple-500/25 transition-all duration-300 h-12"
                  disabled={!selectedProvider || !codePrompt || isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                      Generating Code...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate Code
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {generatedCode && (
              <Card className="bg-slate-800/30 backdrop-blur-lg border border-slate-700/50 shadow-xl">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white flex items-center space-x-2">
                      <Code2 className="w-5 h-5 text-emerald-400" />
                      <span>Generated Code</span>
                      <Badge className="bg-emerald-500/20 text-emerald-300 border-emerald-400/30">
                        Ready
                      </Badge>
                    </CardTitle>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={copyCode}
                      className="border-slate-600/50 text-slate-300 hover:bg-slate-700/50 backdrop-blur"
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-slate-900/80 backdrop-blur p-6 rounded-xl overflow-x-auto border border-slate-700/50 shadow-inner">
                      <code className="text-emerald-400 text-sm leading-relaxed">{generatedCode}</code>
                    </pre>
                    <div className="absolute top-3 right-3">
                      <Badge className="bg-blue-500/20 text-blue-300 border-blue-400/30 text-xs">
                        React + TypeScript
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
