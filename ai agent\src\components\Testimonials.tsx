
import { useState, useEffect } from "react";

export const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Senior Developer at TechCorp",
      content: "CodeGenesis has revolutionized our development process. The AI-powered code generation saves us hours every day.",
      rating: 5,
      company: "TechCorp"
    },
    {
      name: "<PERSON>",
      role: "Product Manager at StartupXYZ",
      content: "The integration capabilities are incredible. We can connect multiple AI providers and choose the best one for each task.",
      rating: 5,
      company: "StartupXYZ"
    },
    {
      name: "<PERSON>",
      role: "Freelance Developer",
      content: "As a freelancer, CodeGenesis helps me deliver projects faster while maintaining high quality. It's a game-changer.",
      rating: 5,
      company: "Independent"
    },
    {
      name: "<PERSON>",
      role: "CTO at InnovateLab",
      content: "The enterprise features and security measures give us confidence to use this platform for our most critical projects.",
      rating: 5,
      company: "InnovateLab"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(timer);
  }, [testimonials.length]);

  return (
    <section className="py-20 bg-slate-900/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              What Our Clients Say
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Join thousands of developers who trust CodeGenesis for their AI-powered development needs.
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          <div className="bg-slate-800/50 rounded-2xl p-8 md:p-12 border border-slate-700">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                  <span key={i} className="text-yellow-400 text-2xl">★</span>
                ))}
              </div>
              
              <blockquote className="text-xl md:text-2xl text-slate-100 mb-8 leading-relaxed">
                "{testimonials[currentIndex].content}"
              </blockquote>
              
              <div className="flex items-center justify-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-600/30 to-purple-600/30 flex items-center justify-center border border-slate-700">
                  <span className="text-lg">👤</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-white">{testimonials[currentIndex].name}</div>
                  <div className="text-slate-400">{testimonials[currentIndex].role}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                  index === currentIndex ? "bg-blue-500" : "bg-slate-600"
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
