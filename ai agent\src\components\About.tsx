
import { Spark<PERSON>, Zap, Shield, Users } from "lucide-react";

export const About = () => {
  const features = [
    { icon: Sparkles, text: "AI-Powered Code Generation", color: "from-blue-400 to-cyan-400" },
    { icon: Zap, text: "Real-time Collaboration", color: "from-purple-400 to-pink-400" },
    { icon: Shield, text: "Enterprise Security", color: "from-green-400 to-emerald-400" },
    { icon: Users, text: "Multiple AI Providers", color: "from-orange-400 to-red-400" }
  ];

  return (
    <section id="about" className="py-32 relative overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/50 to-slate-950/80"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(59,130,246,0.1),transparent_50%)] animate-pulse-slow"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(147,51,234,0.1),transparent_50%)] animate-pulse-slow animation-delay-1000"></div>
      
      {/* Floating elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-xl animate-float-delayed"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20 animate-fade-in-up">
          <div className="inline-flex items-center justify-center p-2 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full mb-8 backdrop-blur-md border border-blue-500/20 hover:bg-gradient-to-r hover:from-blue-600/30 hover:to-purple-600/30 transition-all duration-500 group">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium group-hover:scale-105 transition-transform duration-300">
              About CodeGenesis
            </div>
          </div>
          
          <h2 className="text-5xl md:text-7xl font-bold mb-8">
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-gradient-x">
              Pioneering the Future
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed animate-fade-in animation-delay-300">
            We're revolutionizing software development with AI-powered tools that make coding accessible, 
            efficient, and incredibly powerful for developers worldwide.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8 animate-slide-in-left">
            <div className="space-y-6">
              <h3 className="text-3xl font-bold text-white hover:text-blue-400 transition-colors duration-300">Our Mission</h3>
              <p className="text-slate-300 text-lg leading-relaxed hover:text-slate-200 transition-colors duration-300">
                At CodeGenesis, we believe that great software should be accessible to everyone. Our AI-powered platform 
                democratizes software development by providing intelligent code generation, real-time collaboration, 
                and seamless integration with modern development workflows.
              </p>
              <p className="text-slate-300 text-lg leading-relaxed hover:text-slate-200 transition-colors duration-300">
                Whether you're a seasoned developer looking to accelerate your workflow or a newcomer taking your first 
                steps into programming, our platform adapts to your skill level and helps you achieve your goals faster.
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600/10 to-purple-600/10 p-6 backdrop-blur-md border border-slate-700/50 hover:border-blue-500/50 transition-all duration-500 hover:scale-105 hover:-translate-y-2">
                <div className="text-3xl font-bold text-blue-400 mb-2 group-hover:scale-125 transition-transform duration-500">2023</div>
                <div className="text-sm text-slate-300 group-hover:text-white transition-colors duration-300">Founded</div>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
              <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600/10 to-pink-600/10 p-6 backdrop-blur-md border border-slate-700/50 hover:border-purple-500/50 transition-all duration-500 hover:scale-105 hover:-translate-y-2 animation-delay-200">
                <div className="text-3xl font-bold text-purple-400 mb-2 group-hover:scale-125 transition-transform duration-500">50+</div>
                <div className="text-sm text-slate-300 group-hover:text-white transition-colors duration-300">Team Members</div>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>
          
          <div className="relative animate-slide-in-right">
            <div className="relative rounded-3xl overflow-hidden backdrop-blur-xl border border-slate-700/50 bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 hover:border-slate-600/70 transition-all duration-500 group">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-pink-600/10 group-hover:from-blue-600/15 group-hover:via-purple-600/10 group-hover:to-pink-600/15 transition-all duration-500"></div>
              
              <div className="relative space-y-6">
                {features.map((feature, index) => (
                  <div 
                    key={index} 
                    className="group/item flex items-center space-x-4 p-4 rounded-xl hover:bg-white/5 transition-all duration-500 hover:scale-105 hover:-translate-x-2"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} p-0.5 group-hover/item:scale-125 group-hover/item:rotate-12 transition-all duration-500`}>
                      <div className="w-full h-full bg-slate-900 rounded-lg flex items-center justify-center">
                        <feature.icon className="w-6 h-6 text-white group-hover/item:scale-110 transition-transform duration-300" />
                      </div>
                    </div>
                    <span className="text-slate-300 group-hover/item:text-white transition-colors duration-300 font-medium">
                      {feature.text}
                    </span>
                  </div>
                ))}
              </div>
              
              {/* Enhanced floating orbs */}
              <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse-slow"></div>
              <div className="absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse-slow animation-delay-1000"></div>
              <div className="absolute top-1/2 right-8 w-8 h-8 bg-gradient-to-br from-cyan-400/30 to-blue-400/30 rounded-full blur-md animate-float"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
