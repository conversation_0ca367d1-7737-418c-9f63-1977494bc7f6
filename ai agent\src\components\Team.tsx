
export const Team = () => {
  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Co-Founder",
      bio: "Former AI researcher at Google, passionate about democratizing software development.",
      image: "/placeholder.svg",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#"
      }
    },
    {
      name: "<PERSON>",
      role: "CTO & Co-Founder",
      bio: "Full-stack architect with 15+ years in enterprise software development.",
      image: "/placeholder.svg",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#"
      }
    },
    {
      name: "<PERSON>",
      role: "Head of AI",
      bio: "PhD in Machine Learning, leading our AI research and development initiatives.",
      image: "/placeholder.svg",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#"
      }
    },
    {
      name: "<PERSON>",
      role: "Lead Frontend Developer",
      bio: "UI/UX expert creating beautiful and intuitive user experiences.",
      image: "/placeholder.svg",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#"
      }
    }
  ];

  return (
    <section id="team" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Meet Our Team
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Our diverse team of experts is passionate about pushing the boundaries of AI-powered development.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {team.map((member, index) => (
            <div 
              key={index}
              className="group text-center"
            >
              <div className="relative mb-6">
                <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-600/30 to-purple-600/30 flex items-center justify-center border-2 border-slate-700 group-hover:border-blue-500/50 transition-colors duration-300">
                  <div className="text-4xl">👤</div>
                </div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-600/0 to-purple-600/0 group-hover:from-blue-600/10 group-hover:to-purple-600/10 transition-all duration-300"></div>
              </div>
              
              <h3 className="text-xl font-bold text-white mb-1 group-hover:text-blue-400 transition-colors">
                {member.name}
              </h3>
              <p className="text-blue-400 font-medium mb-3">{member.role}</p>
              <p className="text-slate-300 text-sm leading-relaxed mb-4">
                {member.bio}
              </p>
              
              <div className="flex justify-center space-x-3">
                <a href={member.social.linkedin} className="text-slate-400 hover:text-blue-400 transition-colors">
                  <div className="w-8 h-8 rounded bg-slate-800 flex items-center justify-center hover:bg-blue-600/20">
                    <span className="text-xs">in</span>
                  </div>
                </a>
                <a href={member.social.twitter} className="text-slate-400 hover:text-blue-400 transition-colors">
                  <div className="w-8 h-8 rounded bg-slate-800 flex items-center justify-center hover:bg-blue-600/20">
                    <span className="text-xs">𝕏</span>
                  </div>
                </a>
                <a href={member.social.github} className="text-slate-400 hover:text-blue-400 transition-colors">
                  <div className="w-8 h-8 rounded bg-slate-800 flex items-center justify-center hover:bg-blue-600/20">
                    <span className="text-xs">gh</span>
                  </div>
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
