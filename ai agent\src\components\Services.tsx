
import { Button } from "@/components/ui/button";
import { Code, Zap, Building, Star, ArrowRight } from "lucide-react";

export const Services = () => {
  const services = [
    {
      title: "AI Code Generation",
      description: "Generate high-quality code from natural language descriptions using advanced AI models.",
      features: ["Multi-language support", "Context-aware generation", "Code optimization", "Error detection"],
      price: "Starting at $29/month",
      popular: false,
      icon: Code,
      gradient: "from-blue-600 to-cyan-600"
    },
    {
      title: "Custom API Integration",
      description: "Connect and manage multiple AI providers with our unified API management system.",
      features: ["Multiple AI providers", "Custom endpoints", "Rate limiting", "Usage analytics"],
      price: "Starting at $99/month",
      popular: true,
      icon: Zap,
      gradient: "from-purple-600 to-pink-600"
    },
    {
      title: "Enterprise Solutions",
      description: "Full-scale AI development platform with advanced security and collaboration features.",
      features: ["Team collaboration", "Advanced security", "Custom deployment", "Priority support"],
      price: "Contact for pricing",
      popular: false,
      icon: Building,
      gradient: "from-emerald-600 to-teal-600"
    }
  ];

  return (
    <section id="services" className="py-32 relative overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-950 to-slate-900"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(147,51,234,0.1),transparent_50%)] animate-pulse-slow"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(59,130,246,0.1),transparent_50%)] animate-pulse-slow animation-delay-1500"></div>

      {/* Floating geometric shapes */}
      <div className="absolute top-32 left-16 w-24 h-24 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-xl animate-float"></div>
      <div className="absolute bottom-32 right-16 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-xl animate-float-delayed"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20 animate-fade-in-up">
          <div className="inline-flex items-center justify-center p-2 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full mb-8 backdrop-blur-md border border-purple-500/20 hover:bg-gradient-to-r hover:from-purple-600/30 hover:to-pink-600/30 transition-all duration-500 group">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-medium group-hover:scale-105 transition-transform duration-300">
              Our Services
            </div>
          </div>
          
          <h2 className="text-5xl md:text-7xl font-bold mb-8">
            <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent animate-gradient-x">
              Choose Your Plan
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed animate-fade-in animation-delay-300">
            From individual developers to enterprise teams, we have the perfect solution for your development needs.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div 
              key={index} 
              className={`group relative overflow-hidden rounded-3xl border backdrop-blur-xl transition-all duration-700 hover:scale-105 hover:-translate-y-4 cursor-pointer animate-fade-in-up ${
                service.popular 
                  ? "bg-gradient-to-br from-purple-900/30 to-pink-900/30 border-purple-500/50 shadow-2xl shadow-purple-500/25 hover:shadow-purple-500/40" 
                  : "bg-gradient-to-br from-slate-800/30 to-slate-900/30 border-slate-700/50 hover:border-slate-600/70 hover:shadow-2xl hover:shadow-slate-500/10"
              }`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {service.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10 animate-bounce-slow">
                  <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-medium flex items-center space-x-1 shadow-lg hover:scale-110 transition-transform duration-300">
                    <Star className="w-4 h-4 animate-spin-slow" />
                    <span>Most Popular</span>
                  </div>
                </div>
              )}
              
              {/* Enhanced background gradient overlay */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-5 group-hover:opacity-15 transition-all duration-700`}></div>
              
              {/* Glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-10 blur-xl transition-all duration-700`}></div>
              
              <div className="relative p-8">
                {/* Enhanced Icon */}
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${service.gradient} p-0.5 mb-6 group-hover:scale-125 group-hover:rotate-12 transition-all duration-700`}>
                  <div className="w-full h-full bg-slate-900 rounded-xl flex items-center justify-center">
                    <service.icon className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-slate-300 mb-6 leading-relaxed group-hover:text-slate-200 transition-colors duration-300">{service.description}</p>
                  <div className={`text-4xl font-bold bg-gradient-to-r ${service.gradient} bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300`}>
                    {service.price}
                  </div>
                </div>
                
                <ul className="space-y-4 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <li 
                      key={featureIndex} 
                      className="flex items-center space-x-3 group-hover:translate-x-2 transition-transform duration-300"
                      style={{ transitionDelay: `${featureIndex * 0.1}s` }}
                    >
                      <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${service.gradient} group-hover:scale-150 transition-transform duration-300`}></div>
                      <span className="text-slate-300 group-hover:text-slate-200 transition-colors duration-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  className={`w-full group/btn overflow-hidden relative ${
                    service.popular 
                      ? "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40" 
                      : "bg-slate-800 hover:bg-slate-700 text-white border border-slate-600 hover:border-slate-500"
                  } transition-all duration-500 hover:scale-105 hover:-translate-y-1`}
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4 group-hover/btn:translate-x-2 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-1000"></div>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
