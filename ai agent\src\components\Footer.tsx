
export const Footer = () => {
  const footerLinks = {
    Product: ["Features", "Pricing", "API Docs", "Integrations"],
    Company: ["About", "Team", "Careers", "Press"],
    Resources: ["Blog", "Documentation", "Help Center", "Community"],
    Legal: ["Privacy", "Terms", "Security", "Compliance"]
  };

  return (
    <footer className="bg-slate-950 border-t border-slate-800 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          <div className="lg:col-span-2">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
              CodeGenesis
            </h3>
            <p className="text-slate-300 mb-6 max-w-md">
              Empowering developers with AI-powered tools for faster, smarter software development.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors">
                <div className="w-10 h-10 rounded bg-slate-800 flex items-center justify-center hover:bg-blue-600/20">
                  <span className="text-sm">𝕏</span>
                </div>
              </a>
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors">
                <div className="w-10 h-10 rounded bg-slate-800 flex items-center justify-center hover:bg-blue-600/20">
                  <span className="text-sm">in</span>
                </div>
              </a>
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors">
                <div className="w-10 h-10 rounded bg-slate-800 flex items-center justify-center hover:bg-blue-600/20">
                  <span className="text-sm">gh</span>
                </div>
              </a>
            </div>
          </div>
          
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h4 className="font-semibold text-white mb-4">{category}</h4>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link}>
                    <a href="#" className="text-slate-400 hover:text-white transition-colors">
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        <div className="border-t border-slate-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-slate-400">© 2024 CodeGenesis. All rights reserved.</p>
          <p className="text-slate-400 mt-2 md:mt-0">
            Built with 💙 for developers worldwide
          </p>
        </div>
      </div>
    </footer>
  );
};
