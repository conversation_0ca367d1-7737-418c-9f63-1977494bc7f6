
import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON>, Zap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export const Hero = () => {
  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Enhanced Background with smooth transitions */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-950 via-blue-950/30 to-purple-950/30 transition-all duration-1000"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(59,130,246,0.15),transparent_50%)] animate-pulse"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_60%,rgba(147,51,234,0.15),transparent_50%)] animate-pulse" style={{animationDelay: '2s'}}></div>
      
      {/* Floating geometric shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-xl animate-float"></div>
        <div className="absolute top-3/4 right-1/4 w-48 h-48 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-xl animate-float-delayed"></div>
        <div className="absolute top-1/2 left-3/4 w-24 h-24 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-full blur-xl animate-float-slow"></div>
      </div>
      
      {/* Enhanced animated particles */}
      <div className="absolute inset-0">
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-40 animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-fade-in-up">
          {/* Enhanced floating badge */}
          <div className="flex justify-center mb-8">
            <div className="group backdrop-blur-md bg-white/5 border border-white/10 rounded-full px-6 py-2 flex items-center space-x-2 hover:bg-white/10 hover:border-white/20 transition-all duration-500 hover:scale-105">
              <Sparkles className="w-4 h-4 text-yellow-400 animate-spin-slow" />
              <span className="text-sm text-slate-300 group-hover:text-white transition-colors duration-300">AI-Powered Development Platform</span>
            </div>
          </div>
          
          {/* Enhanced title with staggered animation */}
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-bold leading-tight">
              <span className="inline-block bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-2xl animate-slide-in-left">
                Build the
              </span>
              <br />
              <div className="flex justify-center items-center space-x-4">
                <span className="inline-block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-slide-in-right animation-delay-300">
                  Future
                </span>
                <span className="inline-block bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 bg-clip-text text-transparent animate-slide-in-up animation-delay-600">
                  with AI
                </span>
              </div>
            </h1>
          </div>
          
          {/* Enhanced description */}
          <p className="text-xl md:text-2xl text-slate-300 mb-10 max-w-4xl mx-auto leading-relaxed animate-fade-in animation-delay-900">
            Transform your ideas into reality with our cutting-edge AI development tools. 
            Generate code, build applications, and collaborate with intelligent agents.
          </p>
          
          {/* Enhanced CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16 animate-fade-in-up animation-delay-1200">
            <Button 
              size="lg" 
              className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 text-lg shadow-2xl shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-500 hover:scale-110 hover:-translate-y-1"
            >
              <Code className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
              Start Building
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-2 transition-transform duration-300" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="group border-2 border-slate-600 text-slate-300 hover:text-white hover:bg-slate-800/50 backdrop-blur-md px-10 py-4 text-lg hover:border-blue-400 transition-all duration-500 hover:scale-105 hover:-translate-y-1"
            >
              <Zap className="mr-2 h-5 w-5 group-hover:scale-125 transition-transform duration-300" />
              Watch Demo
            </Button>
          </div>
          
          {/* Enhanced stats cards with staggered animations */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              { value: "10M+", label: "Lines of Code Generated", color: "blue", icon: Code, delay: "0s" },
              { value: "50K+", label: "Active Developers", color: "purple", icon: Sparkles, delay: "0.2s" },
              { value: "99.9%", label: "Uptime Guaranteed", color: "pink", icon: Zap, delay: "0.4s" }
            ].map((stat, index) => (
              <div 
                key={index}
                className="group relative overflow-hidden animate-slide-in-up"
                style={{ animationDelay: `${1.5 + index * 0.2}s` }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl backdrop-blur-xl border border-slate-700/50 group-hover:border-blue-500/50 group-hover:bg-slate-700/60 transition-all duration-500"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/0 to-purple-600/0 group-hover:from-blue-600/10 group-hover:to-purple-600/10 rounded-2xl transition-all duration-500"></div>
                <div className="relative p-8 text-center">
                  <div className="mb-4 inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 group-hover:from-blue-600/30 group-hover:to-purple-600/30 transition-all duration-500 group-hover:scale-110 group-hover:rotate-12">
                    <stat.icon className="w-6 h-6 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                  </div>
                  <div className={`text-4xl font-bold text-${stat.color}-400 mb-2 group-hover:scale-125 transition-transform duration-500`}>
                    {stat.value}
                  </div>
                  <div className="text-slate-300 text-sm group-hover:text-slate-200 transition-colors duration-300">{stat.label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Enhanced scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-slow">
        <div className="w-6 h-10 border-2 border-slate-400 rounded-full flex justify-center backdrop-blur-md bg-white/5 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
          <div className="w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full mt-2 animate-pulse group-hover:h-4 transition-all duration-300"></div>
        </div>
      </div>
    </section>
  );
};
